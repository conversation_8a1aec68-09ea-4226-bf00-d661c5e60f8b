#!/bin/bash

# Deploy Redis to Kubernetes cluster
echo "Deploying Redis to Kubernetes..."

# Apply namespace
echo "Creating Redis namespace..."
kubectl apply -f namespace.yaml

# Apply ConfigMap
echo "Creating Redis ConfigMap..."
kubectl apply -f configmap.yaml

# Apply PVC
echo "Creating Redis PVC..."
kubectl apply -f pvc.yaml

# Apply Deployment
echo "Creating Redis Deployment..."
kubectl apply -f deployment.yaml

# Apply Service
echo "Creating Redis Service..."
kubectl apply -f service.yaml

# Wait for deployment to be ready
echo "Waiting for Redis deployment to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/redis -n redis

# Check status
echo "Redis deployment status:"
kubectl get pods -n redis
kubectl get svc -n redis

echo "Redis deployment completed!"
echo "Redis is accessible at: redis-service.redis.svc.cluster.local:6379"
