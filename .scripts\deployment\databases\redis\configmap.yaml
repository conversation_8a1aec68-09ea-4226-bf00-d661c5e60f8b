apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: redis
data:
  redis.conf: |
    # Redis configuration without authentication
    bind 0.0.0.0
    port 6379
    protected-mode no
    
    # Persistence settings
    save 900 1
    save 300 10
    save 60 10000
    
    # Memory settings
    maxmemory 256mb
    maxmemory-policy allkeys-lru
    
    # Logging
    loglevel notice
    
    # Disable authentication
    # requirepass is commented out for no auth setup
