import { $err } from "cm_net";
import { $fn } from "../../../core/fn";
import { gatewayStore } from "../../../../instance";

$fn("r.add.contact", async function (uid: string) {
    if (!this.user.uid) throw $err.noUser();

    const uids = [this.user.uid, uid];
    uids.sort();

    const rid = uids.join("|");

    // add room
    await gatewayStore.roomPut({
        id: rid,
        type: "contact",
    });

    // add access
    await gatewayStore.roomUserSet(rid, this.user.uid, "contact");
    await gatewayStore.roomUserSet(rid, uid, "contact");

    return rid;
});
