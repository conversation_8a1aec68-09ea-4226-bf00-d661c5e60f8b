# Redis Deployment on Kubernetes

This directory contains the Kubernetes configuration files for deploying Redis without authentication.

## Files

- `namespace.yaml` - Creates the `redis` namespace
- `configmap.yaml` - Redis configuration without authentication
- `pvc.yaml` - Persistent Volume Claim for Redis data storage
- `deployment.yaml` - Redis deployment configuration
- `service.yaml` - Service to expose Redis internally
- `deploy-redis.sh` - Deployment script (for Linux/macOS)

## Deployment

### Manual Deployment (Windows)

```powershell
# Navigate to the redis directory
cd .scripts/deployment/databases/redis

# Apply all configurations
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f pvc.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml

# Check deployment status
kubectl get pods -n redis
kubectl get svc -n redis
```

### Using the deployment script (Linux/macOS)

```bash
chmod +x deploy-redis.sh
./deploy-redis.sh
```

## Access

Redis is accessible within the cluster at:
- **Host**: `redis-service.redis.svc.cluster.local`
- **Port**: `6379`
- **Authentication**: None (disabled for this deployment)

## Testing

Test the Redis connection:

```bash
# Test ping
kubectl exec -it deployment/redis -n redis -- redis-cli ping

# Test set/get
kubectl exec -it deployment/redis -n redis -- redis-cli set test "hello"
kubectl exec -it deployment/redis -n redis -- redis-cli get test
```

## Configuration

The Redis instance is configured with:
- No authentication (protected-mode disabled)
- Persistence enabled with RDB snapshots
- Memory limit: 256MB with LRU eviction policy
- Storage: 1GB persistent volume using local-path storage class

## Cleanup

To remove the Redis deployment:

```bash
kubectl delete namespace redis
```

This will remove all Redis resources including the persistent volume claim.
